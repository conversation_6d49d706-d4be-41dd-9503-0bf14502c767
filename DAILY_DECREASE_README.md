# Thống kê theo dõi giảm Diamond và Bean theo ngày

## <PERSON><PERSON> tả
Tính năng mới này bổ sung thêm cột "Giảm trong ngày" vào card "Thống kê theo ngày" để theo dõi tổng lượng Diamond và Bean bị giảm trong từng ngày cụ thể.

## Tính năng đã thêm

### 1. Cột mới trong bảng "Thống kê theo ngày"
- **Tên cột**: "Giảm trong ngày"
- **Nội dung hiển thị**:
  - 🔴 Tổng Diamond giảm trong ngày (với icon gem)
  - 🟡 Tổng Bean giảm trong ngày (với icon coins)
  - Số Self UID bị ảnh hưởng
  - Tổng số lần giảm xảy ra

### 2. Logic tính toán mới
- **Method `getDailyDecreaseStats()`**: Tính toán lượng giảm trong một ngày cụ thể
- **Cập nhật `getDailyStats()`**: <PERSON><PERSON> sung thông tin giảm vào thống kê hàng ngày

### 3. Cách hoạt động
1. **Phân tích theo ngày**: Với mỗi ngày, hệ thống sẽ:
   - Lấy tất cả Self UID có hoạt động trong ngày đó
   - Phân tích từng cặp records liên tiếp của mỗi Self UID
   - Tính tổng lượng giảm Diamond và Bean (chỉ tính khi giảm, không tính khi tăng)
   - Đếm số lần giảm và số Self UID bị ảnh hưởng

2. **Hiển thị thông minh**:
   - Chỉ hiển thị badge khi có giảm thực tế
   - Hiển thị "Không có giảm" khi không có lượng giảm nào
   - Thông tin chi tiết: số UID ảnh hưởng và số lần giảm

## Ví dụ hiển thị

```
Giảm trong ngày:
💎 -3,000 Diamond
🪙 -10,000 Bean
2 UID, 4 lần
```

## Files đã thay đổi

1. **Controller**: `SelfUidTrackingController.php`
   - Thêm method `getDailyDecreaseStats()` - tính lượng giảm trong ngày
   - Cập nhật method `getDailyStats()` - bổ sung thông tin giảm

2. **View**: `index.blade.php`
   - Thêm cột "Giảm trong ngày" vào bảng thống kê
   - Hiển thị badge với icon và thông tin chi tiết

## Cách test

### 1. Tạo demo data
```bash
php demo_daily_decrease.php
```

### 2. Test logic tính toán
```bash
php test_daily_decrease.php
```

### 3. Xem kết quả
Truy cập trang SelfUID Tracking và xem cột "Giảm trong ngày" trong card "Thống kê theo ngày".

## Lợi ích
- **Theo dõi chi tiết**: Biết được chính xác lượng Diamond/Bean giảm trong từng ngày
- **Phát hiện bất thường**: Dễ dàng nhận biết những ngày có lượng giảm cao
- **Thống kê toàn diện**: Kết hợp với thống kê tăng/giảm tổng thể để có cái nhìn đầy đủ
- **Hỗ trợ filter**: Hoạt động với tất cả filter hiện có (user_token, self_uid, khoảng thời gian)
