<?php

// Test script để kiểm tra logic tính toán daily decrease stats
require_once 'public_html/vendor/autoload.php';

// Khởi tạo Laravel app
$app = require_once 'public_html/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Http\Controllers\SelfUidTrackingController;
use Illuminate\Http\Request;
use Carbon\Carbon;

echo "Testing daily decrease stats calculation...\n\n";

// Tạo controller instance
$controller = new SelfUidTrackingController();

// Tạo request giả
$request = new Request();

// Test với user_token cụ thể
$request->merge(['user_token' => 'daily_demo_001']);

echo "Testing getDailyStats with daily decrease information...\n";

// Sử dụng reflection để gọi private method
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('getDailyStats');
$method->setAccessible(true);

$dailyStats = $method->invoke($controller, $request);

echo "Found " . $dailyStats->count() . " days with data:\n\n";

foreach ($dailyStats as $stat) {
    echo "Date: " . Carbon::parse($stat->date)->format('d/m/Y') . "\n";
    echo "  - Unique Self UIDs: {$stat->unique_self_uids}\n";
    echo "  - Total Diamond: " . number_format($stat->total_diamond) . "\n";
    echo "  - Total Bean: " . number_format($stat->total_bean) . "\n";
    echo "  - Diamond Change: {$stat->diamond_change}\n";
    echo "  - Bean Change: {$stat->bean_change}\n";
    echo "  - Daily Diamond Decrease: " . number_format($stat->daily_diamond_decrease) . "\n";
    echo "  - Daily Bean Decrease: " . number_format($stat->daily_bean_decrease) . "\n";
    echo "  - Daily Decrease Count: {$stat->daily_decrease_count}\n";
    echo "  - Affected UIDs Count: {$stat->affected_uids_count}\n";
    echo "\n";
}

echo "Test completed!\n";
