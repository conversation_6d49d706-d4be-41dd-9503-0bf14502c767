<?php

// Demo script để tạo dữ liệu test cho tính năng theo dõi giảm theo ngày
require_once 'public_html/vendor/autoload.php';

// Khởi tạo Laravel app
$app = require_once 'public_html/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\SelfUidTracking;
use App\Models\User;
use Carbon\Carbon;

echo "Creating demo data for daily decrease tracking...\n\n";

// Tạo demo user
$user = User::firstOrCreate([
    'user_token' => 'daily_demo_001'
], [
    'name' => 'Daily Demo User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password')
]);

echo "Created/Found user: {$user->name} ({$user->user_token})\n";

// Tạo dữ liệu cho 3 ngày gần đây với nhiều lần giảm trong ngày
$dates = [
    Carbon::now()->subDays(2)->startOfDay(),
    Carbon::now()->subDays(1)->startOfDay(),
    Carbon::now()->startOfDay()
];

foreach ($dates as $index => $date) {
    echo "\nCreating data for date: " . $date->format('Y-m-d') . "\n";
    
    // Self UID 1 - có nhiều lần giảm trong ngày
    $records = [
        [
            'user_token' => 'daily_demo_001',
            'self_uid' => 'daily_uid_001',
            'diamond_balance' => 15000 - ($index * 2000),
            'bean_balance' => 75000 - ($index * 10000),
            'room_count' => 8,
            'created_at' => $date->copy()->addHours(8)
        ],
        [
            'user_token' => 'daily_demo_001',
            'self_uid' => 'daily_uid_001',
            'diamond_balance' => 13500 - ($index * 2000), // Giảm 1500
            'bean_balance' => 70000 - ($index * 10000), // Giảm 5000
            'room_count' => 7,
            'created_at' => $date->copy()->addHours(12)
        ],
        [
            'user_token' => 'daily_demo_001',
            'self_uid' => 'daily_uid_001',
            'diamond_balance' => 12000 - ($index * 2000), // Giảm thêm 1500
            'bean_balance' => 65000 - ($index * 10000), // Giảm thêm 5000
            'room_count' => 6,
            'created_at' => $date->copy()->addHours(16)
        ],
        
        // Self UID 2 - ít giảm hơn
        [
            'user_token' => 'daily_demo_001',
            'self_uid' => 'daily_uid_002',
            'diamond_balance' => 8000 - ($index * 1000),
            'bean_balance' => 40000 - ($index * 5000),
            'room_count' => 4,
            'created_at' => $date->copy()->addHours(10)
        ],
        [
            'user_token' => 'daily_demo_001',
            'self_uid' => 'daily_uid_002',
            'diamond_balance' => 7000 - ($index * 1000), // Giảm 1000
            'bean_balance' => 37000 - ($index * 5000), // Giảm 3000
            'room_count' => 4,
            'created_at' => $date->copy()->addHours(14)
        ]
    ];
    
    foreach ($records as $record) {
        SelfUidTracking::create($record);
        echo "  Created: {$record['self_uid']} at {$record['created_at']->format('H:i')} - Diamond: {$record['diamond_balance']}, Bean: {$record['bean_balance']}\n";
    }
}

echo "\nDemo data for daily decrease tracking created successfully!\n";
echo "You can now see the daily decrease statistics in the 'Thống kê theo ngày' card.\n";
